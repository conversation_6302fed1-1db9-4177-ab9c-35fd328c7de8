import axiosInstance from 'src/utils/axios';
import { useApiServices } from 'src/services/hooks/use-api-services';

// Define the API endpoints for user profile
export const profileEndpoints = {
  me: '/users/me',
  changePassword: '/users/change-password',
};

// Define the User Profile data type (based on actual API response)
export interface UserProfile {
  id: string;
  email: string;
  name?: string;
  username?: string;
  avatar?: string;
  role?: string;
  isActive?: boolean;
  createdAt?: string;
  updatedAt?: string;
  // Legacy fields for backward compatibility
  firstName?: string;
  lastName?: string;
  phone?: string;
  bio?: string;
}

// Define the update profile payload type (only fields accepted by backend)
export interface UpdateProfilePayload {
  name?: string;
  username?: string;
  avatar?: string;
}

// Define the UI form data type (for form management)
export interface ProfileFormData {
  name: string;
  username: string;
  avatar?: string;
  // Additional fields for UI only (not sent to API)
  firstName: string;
  lastName: string;
  phone: string;
  bio: string;
}

// Define the change password payload type (for API request)
export interface ChangePasswordPayload {
  currentPassword: string;
  newPassword: string;
}

// Define the change password form type (for UI form)
export interface ChangePasswordFormData {
  currentPassword: string;
  newPassword: string;
  confirmPassword: string;
}

// Define the change password response type
export interface ChangePasswordResponse {
  message: string;
  success: boolean;
}

// Define the delete account response type
export interface DeleteAccountResponse {
  message: string;
  success: boolean;
}

// Create a hook to use the profile API
export const useProfileApi = () => {
  const apiServices = useApiServices({ axiosInstance });

  // Get current user profile
  const useGetUserProfile = () => {
    return apiServices.useGetItemService<UserProfile>({
      url: profileEndpoints.me,
    });
  };

  // Update user profile
  const useUpdateUserProfile = (onSuccess?: (data: UserProfile) => void) => {
    return apiServices.usePatchService<UpdateProfilePayload>({
      url: profileEndpoints.me,
      withFormData: false,
      onSuccess,
      queryKey: profileEndpoints.me + 'item',
    });
  };

  // Delete user account
  const useDeleteUserAccount = (onSuccess?: () => void) => {
    return apiServices.useDeleteService<DeleteAccountResponse>({
      url: profileEndpoints.me,
      onSuccess,
    });
  };

  // Change password
  const useChangePassword = (onSuccess?: (data: ChangePasswordResponse) => void) => {
    return apiServices.usePostService<ChangePasswordPayload, ChangePasswordResponse>({
      url: profileEndpoints.changePassword,
      withFormData: false,
      onSuccess,
    });
  };

  return {
    useGetUserProfile,
    useUpdateUserProfile,
    useDeleteUserAccount,
    useChangePassword,
  };
};
