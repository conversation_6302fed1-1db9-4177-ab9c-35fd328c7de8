import { useState, useEffect } from 'react';
import { useProfileApi, UserProfile, ProfileFormData, ChangePasswordFormData } from 'src/services/api/use-profile-api';

export const useProfileView = () => {
  // Form state for profile update
  const [profileForm, setProfileForm] = useState<ProfileFormData>({
    name: '',
    username: '',
    firstName: '',
    lastName: '',
    phone: '',
    bio: '',
  });

  // Form state for password change
  const [passwordForm, setPasswordForm] = useState<ChangePasswordFormData>({
    currentPassword: '',
    newPassword: '',
    confirmPassword: '',
  });

  // UI state
  const [isEditing, setIsEditing] = useState(false);
  const [avatarFile, setAvatarFile] = useState<File | null>(null);
  const [avatarPreview, setAvatarPreview] = useState<string>('');

  // Use the profile API hook
  const { 
    useGetUserProfile, 
    useUpdateUserProfile, 
    useDeleteUserAccount, 
    useChangePassword 
  } = useProfileApi();

  // Get user profile data
  const {
    data: userProfile,
    isLoading: isLoadingProfile,
    error: profileError,
    refetch: refetchProfile,
  } = useGetUserProfile();

  // Update profile mutation
  const {
    mutate: updateProfile,
    isPending: isUpdatingProfile,
    error: updateError,
  } = useUpdateUserProfile((data) => {
    setIsEditing(false);
    refetchProfile();
  });

  // Change password mutation
  const {
    mutate: changePassword,
    isPending: isChangingPassword,
    error: passwordError,
  } = useChangePassword(() => {
    // Reset password form on success
    setPasswordForm({
      currentPassword: '',
      newPassword: '',
      confirmPassword: '',
    });
  });

  // Delete account mutation
  const {
    mutate: deleteAccount,
    isPending: isDeletingAccount,
    error: deleteError,
  } = useDeleteUserAccount();

  // Initialize form with user data when profile loads
  useEffect(() => {
    if (userProfile) {
      // Split name into first and last name for UI
      const nameParts = (userProfile.name || '').split(' ');
      const firstName = nameParts[0] || '';
      const lastName = nameParts.slice(1).join(' ') || '';

      setProfileForm({
        name: userProfile.name || '',
        username: userProfile.username || '',
        firstName,
        lastName,
        phone: userProfile.phone || '',
        bio: userProfile.bio || '',
      });

      // Set avatar preview if user has an avatar
      if (userProfile.avatar) {
        setAvatarPreview(userProfile.avatar);
      }
    }
  }, [userProfile]);

  // Handle profile form changes
  const handleProfileFormChange = (field: keyof ProfileFormData, value: string) => {
    setProfileForm(prev => {
      const updated = {
        ...prev,
        [field]: value,
      };

      // Auto-update name when first/last name changes
      if (field === 'firstName' || field === 'lastName') {
        updated.name = `${field === 'firstName' ? value : prev.firstName} ${field === 'lastName' ? value : prev.lastName}`.trim();
      }

      return updated;
    });
  };

  // Handle password form changes
  const handlePasswordFormChange = (field: keyof ChangePasswordFormData, value: string) => {
    setPasswordForm(prev => ({
      ...prev,
      [field]: value,
    }));
  };

  // Handle avatar file selection
  const handleAvatarChange = (file: File | null) => {
    setAvatarFile(file);
    if (file) {
      const reader = new FileReader();
      reader.onloadend = () => {
        setAvatarPreview(reader.result as string);
      };
      reader.readAsDataURL(file);
    } else {
      setAvatarPreview(userProfile?.avatar || '');
    }
  };

  // Handle profile update submission
  const handleUpdateProfile = () => {
    // Only send fields that the backend accepts
    const payload = {
      name: profileForm.name,
      username: profileForm.username,
    };

    // If there's a new avatar file, we would typically upload it first
    // For now, we'll just include the avatar URL in the payload
    if (avatarFile) {
      // In a real implementation, you'd upload the file and get a URL
      // payload.avatar = uploadedAvatarUrl;
    }

    updateProfile(payload);
  };

  // Handle password change submission
  const handleChangePassword = () => {
    // Validate passwords match
    if (passwordForm.newPassword !== passwordForm.confirmPassword) {
      // Handle validation error
      return;
    }

    // Only send required fields to API (exclude confirmPassword)
    changePassword({
      currentPassword: passwordForm.currentPassword,
      newPassword: passwordForm.newPassword,
    });
  };

  // Handle account deletion
  const handleDeleteAccount = () => {
    // In a real implementation, you might want to show a confirmation dialog
    deleteAccount({} as any); // The delete endpoint might not need a payload
  };

  // Toggle edit mode
  const handleToggleEdit = () => {
    if (isEditing) {
      // If canceling edit, reset form to original values
      if (userProfile) {
        const nameParts = (userProfile.name || '').split(' ');
        const firstName = nameParts[0] || '';
        const lastName = nameParts.slice(1).join(' ') || '';

        setProfileForm({
          name: userProfile.name || '',
          username: userProfile.username || '',
          firstName,
          lastName,
          phone: userProfile.phone || '',
          bio: userProfile.bio || '',
        });
        setAvatarPreview(userProfile.avatar || '');
        setAvatarFile(null);
      }
    }
    setIsEditing(!isEditing);
  };

  // Check if profile form has changes
  const hasProfileChanges = userProfile ? (
    profileForm.name !== (userProfile.name || '') ||
    profileForm.username !== (userProfile.username || '') ||
    avatarFile !== null
  ) : false;

  // Check if password form is valid
  const isPasswordFormValid = 
    passwordForm.currentPassword.length > 0 &&
    passwordForm.newPassword.length >= 8 &&
    passwordForm.newPassword === passwordForm.confirmPassword;

  return {
    // Data
    userProfile,
    profileForm,
    passwordForm,
    avatarPreview,
    
    // Loading states
    isLoadingProfile,
    isUpdatingProfile,
    isChangingPassword,
    isDeletingAccount,
    
    // Error states
    profileError,
    updateError,
    passwordError,
    deleteError,
    
    // UI state
    isEditing,
    hasProfileChanges,
    isPasswordFormValid,
    
    // Actions
    handleProfileFormChange,
    handlePasswordFormChange,
    handleAvatarChange,
    handleUpdateProfile,
    handleChangePassword,
    handleDeleteAccount,
    handleToggleEdit,
    refetchProfile,
  };
};

export type { UserProfile, ProfileFormData, ChangePasswordFormData };
