import { useState, useEffect } from 'react';
import { useProfileApi, UserProfile, UpdateProfilePayload, ChangePasswordPayload } from 'src/services/api/use-profile-api';

export const useProfileView = () => {
  // Form state for profile update
  const [profileForm, setProfileForm] = useState<UpdateProfilePayload>({
    firstName: '',
    lastName: '',
    username: '',
    phone: '',
    bio: '',
  });

  // Form state for password change
  const [passwordForm, setPasswordForm] = useState<ChangePasswordPayload>({
    currentPassword: '',
    newPassword: '',
    confirmPassword: '',
  });

  // UI state
  const [isEditing, setIsEditing] = useState(false);
  const [avatarFile, setAvatarFile] = useState<File | null>(null);
  const [avatarPreview, setAvatarPreview] = useState<string>('');

  // Use the profile API hook
  const { 
    useGetUserProfile, 
    useUpdateUserProfile, 
    useDeleteUserAccount, 
    useChangePassword 
  } = useProfileApi();

  // Get user profile data
  const {
    data: userProfile,
    isLoading: isLoadingProfile,
    error: profileError,
    refetch: refetchProfile,
  } = useGetUserProfile();

  // Update profile mutation
  const {
    mutate: updateProfile,
    isPending: isUpdatingProfile,
    error: updateError,
  } = useUpdateUserProfile((data) => {
    setIsEditing(false);
    refetchProfile();
  });

  // Change password mutation
  const {
    mutate: changePassword,
    isPending: isChangingPassword,
    error: passwordError,
  } = useChangePassword(() => {
    // Reset password form on success
    setPasswordForm({
      currentPassword: '',
      newPassword: '',
      confirmPassword: '',
    });
  });

  // Delete account mutation
  const {
    mutate: deleteAccount,
    isPending: isDeletingAccount,
    error: deleteError,
  } = useDeleteUserAccount();

  // Initialize form with user data when profile loads
  useEffect(() => {
    if (userProfile) {
      setProfileForm({
        firstName: userProfile.firstName || '',
        lastName: userProfile.lastName || '',
        username: userProfile.username || '',
        phone: userProfile.phone || '',
        bio: userProfile.bio || '',
      });
      
      // Set avatar preview if user has an avatar
      if (userProfile.avatar) {
        setAvatarPreview(userProfile.avatar);
      }
    }
  }, [userProfile]);

  // Handle profile form changes
  const handleProfileFormChange = (field: keyof UpdateProfilePayload, value: string) => {
    setProfileForm(prev => ({
      ...prev,
      [field]: value,
    }));
  };

  // Handle password form changes
  const handlePasswordFormChange = (field: keyof ChangePasswordPayload, value: string) => {
    setPasswordForm(prev => ({
      ...prev,
      [field]: value,
    }));
  };

  // Handle avatar file selection
  const handleAvatarChange = (file: File | null) => {
    setAvatarFile(file);
    if (file) {
      const reader = new FileReader();
      reader.onloadend = () => {
        setAvatarPreview(reader.result as string);
      };
      reader.readAsDataURL(file);
    } else {
      setAvatarPreview(userProfile?.avatar || '');
    }
  };

  // Handle profile update submission
  const handleUpdateProfile = () => {
    const payload: UpdateProfilePayload = { ...profileForm };
    
    // If there's a new avatar file, we would typically upload it first
    // For now, we'll just include the avatar URL in the payload
    if (avatarFile) {
      // In a real implementation, you'd upload the file and get a URL
      // payload.avatar = uploadedAvatarUrl;
    }

    updateProfile(payload);
  };

  // Handle password change submission
  const handleChangePassword = () => {
    // Validate passwords match
    if (passwordForm.newPassword !== passwordForm.confirmPassword) {
      // Handle validation error
      return;
    }

    changePassword(passwordForm);
  };

  // Handle account deletion
  const handleDeleteAccount = () => {
    // In a real implementation, you might want to show a confirmation dialog
    deleteAccount({} as any); // The delete endpoint might not need a payload
  };

  // Toggle edit mode
  const handleToggleEdit = () => {
    if (isEditing) {
      // If canceling edit, reset form to original values
      if (userProfile) {
        setProfileForm({
          firstName: userProfile.firstName || '',
          lastName: userProfile.lastName || '',
          username: userProfile.username || '',
          phone: userProfile.phone || '',
          bio: userProfile.bio || '',
        });
        setAvatarPreview(userProfile.avatar || '');
        setAvatarFile(null);
      }
    }
    setIsEditing(!isEditing);
  };

  // Check if profile form has changes
  const hasProfileChanges = userProfile ? (
    profileForm.firstName !== (userProfile.firstName || '') ||
    profileForm.lastName !== (userProfile.lastName || '') ||
    profileForm.username !== (userProfile.username || '') ||
    profileForm.phone !== (userProfile.phone || '') ||
    profileForm.bio !== (userProfile.bio || '') ||
    avatarFile !== null
  ) : false;

  // Check if password form is valid
  const isPasswordFormValid = 
    passwordForm.currentPassword.length > 0 &&
    passwordForm.newPassword.length >= 8 &&
    passwordForm.newPassword === passwordForm.confirmPassword;

  return {
    // Data
    userProfile,
    profileForm,
    passwordForm,
    avatarPreview,
    
    // Loading states
    isLoadingProfile,
    isUpdatingProfile,
    isChangingPassword,
    isDeletingAccount,
    
    // Error states
    profileError,
    updateError,
    passwordError,
    deleteError,
    
    // UI state
    isEditing,
    hasProfileChanges,
    isPasswordFormValid,
    
    // Actions
    handleProfileFormChange,
    handlePasswordFormChange,
    handleAvatarChange,
    handleUpdateProfile,
    handleChangePassword,
    handleDeleteAccount,
    handleToggleEdit,
    refetchProfile,
  };
};

export type { UserProfile, UpdateProfilePayload, ChangePasswordPayload };
