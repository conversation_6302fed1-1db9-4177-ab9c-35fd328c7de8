import React from 'react';
import { <PERSON>, <PERSON>po<PERSON>, <PERSON><PERSON>, <PERSON>ack, Alert } from '@mui/material';
import { useProfileApi } from 'src/services/api/use-profile-api';

// Example component demonstrating how to use the Profile APIs
export const ProfileApiExample = () => {
  const { 
    useGetUserProfile, 
    useUpdateUserProfile, 
    useDeleteUserAccount, 
    useChangePassword 
  } = useProfileApi();

  // Get user profile
  const { data: profile, isLoading, error, refetch } = useGetUserProfile();

  // Update profile mutation
  const { mutate: updateProfile, isPending: isUpdating } = useUpdateUserProfile((data) => {
    console.log('Profile updated successfully:', data);
    refetch(); // Refresh the profile data
  });

  // Change password mutation
  const { mutate: changePassword, isPending: isChangingPassword } = useChangePassword((data) => {
    console.log('Password changed successfully:', data);
  });

  // Delete account mutation
  const { mutate: deleteAccount, isPending: isDeleting } = useDeleteUserAccount(() => {
    console.log('Account deleted successfully');
    // Redirect to login or home page
  });

  // Example handlers
  const handleUpdateProfile = () => {
    updateProfile({
      name: 'John Doe Updated',
      username: 'johndoe_updated',
    });
  };

  const handleChangePassword = () => {
    // Note: confirmPassword is only used for client-side validation
    // Only currentPassword and newPassword are sent to the API
    changePassword({
      currentPassword: 'currentpass123',
      newPassword: 'newpass123',
    });
  };

  const handleDeleteAccount = () => {
    if (window.confirm('Are you sure you want to delete your account?')) {
      deleteAccount({} as any);
    }
  };

  if (isLoading) return <Typography>Loading profile...</Typography>;
  if (error) return <Alert severity="error">Error: {error.message}</Alert>;

  return (
    <Box sx={{ p: 3, maxWidth: 600 }}>
      <Typography variant="h4" gutterBottom>
        Profile API Example
      </Typography>

      {/* Display Profile Data */}
      <Box sx={{ mb: 3, p: 2, border: '1px solid #ddd', borderRadius: 1 }}>
        <Typography variant="h6" gutterBottom>Current Profile:</Typography>
        <Typography><strong>Name:</strong> {profile?.name || 'Not set'}</Typography>
        <Typography><strong>Email:</strong> {profile?.email}</Typography>
        <Typography><strong>Username:</strong> {profile?.username || 'Not set'}</Typography>
        <Typography><strong>Role:</strong> {profile?.role || 'Not set'}</Typography>
        <Typography><strong>Active:</strong> {profile?.isActive ? 'Yes' : 'No'}</Typography>
      </Box>

      {/* Action Buttons */}
      <Stack spacing={2} direction="row" flexWrap="wrap">
        <Button
          variant="contained"
          onClick={handleUpdateProfile}
          disabled={isUpdating}
        >
          {isUpdating ? 'Updating...' : 'Update Profile'}
        </Button>

        <Button
          variant="outlined"
          onClick={handleChangePassword}
          disabled={isChangingPassword}
        >
          {isChangingPassword ? 'Changing...' : 'Change Password'}
        </Button>

        <Button
          variant="outlined"
          color="error"
          onClick={handleDeleteAccount}
          disabled={isDeleting}
        >
          {isDeleting ? 'Deleting...' : 'Delete Account'}
        </Button>

        <Button
          variant="text"
          onClick={() => refetch()}
        >
          Refresh Profile
        </Button>
      </Stack>

      {/* API Endpoints Reference */}
      <Box sx={{ mt: 4, p: 2, bgcolor: 'grey.100', borderRadius: 1 }}>
        <Typography variant="h6" gutterBottom>API Endpoints:</Typography>
        <Typography variant="body2" component="div">
          • <strong>GET /users/me</strong> - Fetch user profile<br/>
          • <strong>PATCH /users/me</strong> - Update user profile<br/>
          • <strong>DELETE /users/me</strong> - Delete user account<br/>
          • <strong>POST /users/change-password</strong> - Change password
        </Typography>
      </Box>
    </Box>
  );
};

export default ProfileApiExample;
